<template>
  <a-dropdown placement="bottomLeft" trigger="click" :overlayStyle="{ padding: 0 }">
    <a-button type="text" :icon="h(ColumnHeightOutlined)">行高</a-button>
    <template #overlay>
      <a-menu :selectedKeys="selectedLineHeight" @click="handleLineHeightClick">
        <a-menu-item key="low">低</a-menu-item>
        <a-menu-item key="medium">中</a-menu-item>
        <a-menu-item key="high">高</a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
import { ref, onMounted, h } from 'vue'
import { ColumnHeightOutlined } from '@ant-design/icons-vue'

interface Emits {
  (e: 'change', value: string): void
}

const emit = defineEmits<Emits>()

const selectedLineHeight = ref<string[]>(['medium'])

onMounted(() => {
  console.log('初始化')
  emit('change', selectedLineHeight.value[0])
})

const handleLineHeightClick = (e: any) => {
  selectedLineHeight.value = [e.key]
  emit('change', e.key)
}
</script>

<style lang="scss" scoped></style>
