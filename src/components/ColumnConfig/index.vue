<template>
  <a-dropdown
    placement="bottomLeft"
    trigger="click"
    :overlayStyle="{ padding: 0 }"
    v-model:open="dropdownVisible"
  >
    <a-button type="text" :icon="h(SettingOutlined)">字段配置</a-button>
    <template #overlay>
      <div class="column-config-dropdown" @click.stop>
        <ColumnConfigPanel :columns="configurableColumns" @update="handleColumnsUpdate" />
      </div>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { SettingOutlined } from '@ant-design/icons-vue'
import ColumnConfigPanel from './config-panel.vue'
import type { TableColumn } from '@/common/types'

interface Props {
  columns: TableColumn[]
}
interface Emits {
  (e: 'update', columns: TableColumn[]): void
  (e: 'update:visibleColumns', columns: TableColumn[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 内部列配置状态
const internalColumns = ref<TableColumn[]>([...props.columns])

// 下拉菜单显示状态
const dropdownVisible = ref<boolean>(false)

// 过滤出可配置的列（排除操作列等不可配置的列）
const configurableColumns = computed(() =>
  internalColumns.value.filter(col => col.key !== 'action')
)

// 处理后的可见列配置（包含操作列）
const visibleColumns = computed(() => {
  const configurable = internalColumns.value.filter(col => col.key !== 'action')
  const actionColumns = internalColumns.value.filter(col => col.key === 'action')

  // 处理可配置列：过滤可见列并处理 fixed 属性
  const visibleConfigurable = configurable
    .filter(col => col.visible)
    .map(col => ({
      ...col,
      fixed: col.fixed === false ? undefined : col.fixed,
    }))

  // 处理操作列：始终显示，处理 fixed 属性
  const visibleAction = actionColumns.map(col => ({
    ...col,
    fixed: col.fixed === false ? undefined : col.fixed,
  }))

  // 合并结果：可配置列 + 操作列
  return [...visibleConfigurable, ...visibleAction]
})

// 处理列配置更新
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  // 更新可配置列，保持操作列不变
  const actionColumns = internalColumns.value.filter(col => col.key === 'action')
  internalColumns.value = [...newColumns, ...actionColumns]

  // 发射原始配置更新事件
  emit('update', internalColumns.value)
  // 发射处理后的可见列事件
  emit('update:visibleColumns', visibleColumns.value)
}

// 监听 props.columns 变化，同步到内部状态
watch(
  () => props.columns,
  (newColumns) => {
    internalColumns.value = [...newColumns]
  },
  { deep: true },
)

// 监听内部列配置变化，自动发射可见列更新事件
watch(
  visibleColumns,
  (newVisibleColumns) => {
    emit('update:visibleColumns', newVisibleColumns)
  },
  { immediate: true, deep: true },
)
</script>

<style lang="scss" scoped>
// 字段配置下拉面板样式
.column-config-dropdown {
  padding: 0;

  :deep(.ant-dropdown-menu) {
    padding: 0;
    box-shadow: none;
  }
}

// 确保下拉菜单不会因为内部点击而关闭
:deep(.ant-dropdown) {
  .ant-dropdown-menu {
    .ant-dropdown-menu-item {
      padding: 0;
    }
  }
}
</style>
