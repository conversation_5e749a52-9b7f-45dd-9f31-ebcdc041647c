<template>
  <div class="worker-order-form">
    <a-form layout="vertical" :model="form" :rules="rules" :disabled="isReadonly" ref="formRef">
      <a-row :gutter="16">
        <!-- 工单编号 -->
        <a-col :span="8">
          <a-form-item label="工单编号" name="orderNo">
            <a-input v-model:value="form.orderNo" placeholder="请输入,忽略将自动生成" />
          </a-form-item>
        </a-col>

        <!-- 计划时间 -->
        <a-col :span="8">
          <a-form-item label="计划时间" name="planTime" required>
            <template #tooltip>
              <div class="plan-time" :class="{ 'plan-time-expired': isExpired }">
                {{ getRemainingTimeText(form.planTime[1]) }}
              </div>
            </template>
            <a-range-picker
              style="width: 100%"
              v-model:value="form.planTime"
              :show-time="{ format: 'HH:mm' }"
              format="YYYY-MM-DD HH:mm"
            />
          </a-form-item>
        </a-col>

        <!-- 状态 -->
        <a-col :span="8">
          <a-form-item label="状态">
            <div class="status-box">
              <CustomTag color="#000" text="未开始" />
            </div>
          </a-form-item>
        </a-col>

        <!-- 产品信息 -->
        <a-col :span="8">
          <a-form-item label="产品信息" name="productId" required>
            <template #tooltip>
              <div class="product-info">
                <span class="product-info-btn" @click="handleCreateProduct">
                  <PlusOutlined style="margin-right: 4px" /> 创建
                </span>
                <a-divider type="vertical" />
                <span class="product-info-btn" @click="handleAdvancedSelect">
                  <SearchOutlined style="margin-right: 4px" /> 高级选择
                </span>
              </div>
            </template>
            <a-select
              v-model:value="form.productId"
              placeholder="请选择产品"
              @change="handleProductChange"
            >
              <a-select-option
                v-for="product in productOptions"
                :key="product.id"
                :value="product.id"
              >
                {{ product.productName }}
              </a-select-option>
            </a-select>

            <!-- 产品详情展示 -->
            <template #extra>
              <ProductInfoDisplay v-if="selectedProduct" :product="selectedProduct" />
            </template>
          </a-form-item>
        </a-col>

        <!-- 计划数 -->
        <a-col :span="8">
          <a-form-item label="计划数" name="planCount" required>
            <a-input-number
              style="width: 100%"
              v-model:value="form.planCount"
              :min="1"
              placeholder="请输入计划数"
            />

            <!-- 统计信息展示 -->
            <template #extra>
              <StatisticsCard v-if="statisticsData" :data="statisticsData" />
            </template>
          </a-form-item>
        </a-col>

        <!-- 备注 -->
        <a-col :span="8">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="form.remark"
              :autoSize="{ minRows: 5, maxRows: 5 }"
              placeholder="请输入备注信息"
            />
          </a-form-item>
        </a-col>

        <!-- 关联单据 -->
        <a-col :span="8">
          <a-form-item label="关联单据" name="relatedOrder">
            <a-input v-model:value="form.relatedOrder" disabled placeholder="系统自动关联" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import type { ComponentType, ProductInfo, StatisticsData } from '../types'
import { useWorkerOrderForm } from '../composables/useWorkerOrderForm'
import { getRemainingTimeText } from '../utils/timeUtils'
import { formatNumber } from '@/utils'
import CustomTag from '@/components/CustomTag/index.vue'
import ProductInfoDisplay from './ProductInfoDisplay.vue'
import StatisticsCard from './StatisticsCard.vue'

/**
 * 工单基本信息表单组件
 * 职责：
 * 1. 处理工单基本信息的表单输入
 * 2. 表单验证和数据格式化
 * 3. 产品信息选择和展示
 * 4. 统计信息展示
 */

interface Props {
  type: ComponentType
  orderId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  type: 'create',
})

const emit = defineEmits<{
  'update:form': [form: any]
  'product-change': [productId: string]
  'create-product': []
  'advanced-select': []
}>()

// 表单引用
const formRef = ref()

// 使用表单管理 composable
const { form, rules, isExpired, isReadonly, validateForm, resetForm, setFormData, submitForm } =
  useWorkerOrderForm(props.type)

// 产品选项（模拟数据，实际应该从API获取）
const productOptions = ref<ProductInfo[]>([
  {
    id: '1',
    productNo: 'CP20230004',
    productName: 'G031',
    productSpec: '1250T',
    stockCount: 1000,
    unit: '只',
  },
])

// 计算属性：选中的产品信息
const selectedProduct = computed(() => {
  return productOptions.value.find((p) => p.id === form.value.productId)
})

// 计算属性：统计数据（模拟数据）
const statisticsData = computed<StatisticsData | null>(() => {
  if (!form.value.planCount) return null

  return {
    goodCount: 1000,
    badCount: 50,
    defectRate: 5.0,
  }
})

/**
 * 处理产品选择变化
 */
const handleProductChange = (productId: string) => {
  emit('product-change', productId)
}

/**
 * 处理创建产品
 */
const handleCreateProduct = () => {
  emit('create-product')
}

/**
 * 处理高级选择
 */
const handleAdvancedSelect = () => {
  emit('advanced-select')
}

/**
 * 验证表单
 */
const validate = async () => {
  try {
    await formRef.value?.validate()
    return await validateForm()
  } catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

/**
 * 获取表单数据
 */
const getFormData = () => {
  return form.value
}

/**
 * 重置表单数据
 */
const reset = () => {
  formRef.value?.resetFields()
  resetForm()
}

/**
 * 设置表单数据
 */
const setData = (data: any) => {
  setFormData(data)
}

// 监听表单变化，向父组件发送更新
watch(
  () => form.value,
  (newForm) => {
    emit('update:form', newForm)
  },
  { deep: true },
)

// 暴露方法给父组件
defineExpose({
  validate,
  getFormData,
  reset,
  setData,
  submitForm,
})
</script>

<style scoped>
.worker-order-form {
  .plan-time {
    font-size: 12px;
    color: #666;

    &.plan-time-expired {
      color: #ff4d4f;
    }
  }

  .status-box {
    display: flex;
    align-items: center;
  }

  .product-info {
    display: flex;
    align-items: center;
    font-size: 12px;

    .product-info-btn {
      color: #1890ff;
      cursor: pointer;

      &:hover {
        color: #40a9ff;
      }
    }
  }
}
</style>
