<template>
  <div class="configurable-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar" v-if="showToolbar">
      <div class="table-toolbar-left">
        <slot name="toolbar-left">
          <!-- 默认操作按钮 -->
          <a-space v-if="actionButtons.length > 0">
            <a-button
              v-for="button in actionButtons"
              :key="button.key"
              :type="button.type || 'default'"
              :icon="button.icon ? h(button.icon) : undefined"
              @click="button.onClick"
            >
              {{ button.label }}
            </a-button>
          </a-space>
        </slot>
      </div>

      <div class="table-toolbar-right">
        <slot name="toolbar-right">
          <!-- 列配置组件 -->
          <ColumnConfig
            v-if="showColumnConfig"
            :columns="columns"
            @update:visible-columns="handleColumnsUpdate"
          />

          <!-- 行高配置组件 -->
          <LineHeight v-if="showLineHeight" @change="handleLineHeightChange" />
        </slot>
      </div>
    </div>

    <!-- 表格主体 -->
    <a-table
      :row-selection="tableRowSelection"
      :scroll="tableScroll"
      :columns="visibleColumns"
      :data-source="formattedDataSource"
      :loading="loading"
      :customRow="setRowStyle"
      :pagination="tablePagination"
      v-bind="$attrs"
    >
      <!-- 传递所有插槽 -->
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>

      <!-- 默认操作列 -->
      <template #bodyCell="{ column, record, index }">
        <slot name="bodyCell" :column="column" :record="record" :index="index">
          <template v-if="column.key === 'action'">
            <a-space size="small">
              <a
                v-for="action in getRowActions(record)"
                :key="action.key"
                @click="action.onClick(record, index)"
              >
                {{ action.label }}
              </a>
            </a-space>
          </template>
        </slot>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import type { TableColumn } from '@/common/types'
import type { ActionButton } from '../types'
import { useTableConfig } from '../composables/useTableConfig'
import { formatTableData, getTableScrollConfig, createRowSelection } from '../utils/tableUtils'
import ColumnConfig from '@/components/ColumnConfig/index.vue'
import LineHeight from '@/components/LineHeight/index.vue'

/**
 * 通用可配置表格组件
 * 功能特点：
 * 1. 支持列显示/隐藏配置
 * 2. 支持行高调整
 * 3. 支持行选择
 * 4. 支持工具栏自定义
 * 5. 支持操作列自定义
 * 6. 完全的插槽支持
 */

interface Props {
  // 表格列配置
  columns: TableColumn[]
  // 数据源
  dataSource: any[]
  // 是否显示工具栏
  showToolbar?: boolean
  // 是否显示列配置
  showColumnConfig?: boolean
  // 是否显示行高配置
  showLineHeight?: boolean
  // 是否支持行选择
  rowSelection?: boolean
  // 选择类型
  selectionType?: 'checkbox' | 'radio'
  // 工具栏操作按钮
  actionButtons?: ActionButton[]
  // 行操作按钮
  rowActions?: ActionButton[]
  // 加载状态
  loading?: boolean
  // 分页配置
  pagination?: any
  // 表格滚动配置
  scroll?: { x?: number; y?: number }
  // 主键字段
  keyField?: string
}

const props = withDefaults(defineProps<Props>(), {
  showToolbar: true,
  showColumnConfig: true,
  showLineHeight: true,
  rowSelection: false,
  selectionType: 'checkbox',
  actionButtons: () => [],
  rowActions: () => [],
  loading: false,
  pagination: false,
  keyField: 'id',
})

const emit = defineEmits<{
  'update:selectedRowKeys': [keys: (string | number)[]]
  columnsUpdate: [columns: TableColumn[]]
  lineHeightChange: [height: string]
}>()

// 使用表格配置管理
const {
  columns,
  visibleColumns,
  selectedRowKeys,
  updateColumns,
  updateLineHeight,
  setRowStyle,
  handleSelectionChange,
} = useTableConfig(props.columns)

// 计算属性：格式化后的数据源
const formattedDataSource = computed(() => {
  return formatTableData(props.dataSource, props.keyField)
})

// 计算属性：表格滚动配置
const tableScroll = computed(() => {
  if (props.scroll) {
    return props.scroll
  }
  return getTableScrollConfig(props.rowSelection)
})

// 计算属性：行选择配置
const tableRowSelection = computed(() => {
  if (!props.rowSelection) return undefined

  return createRowSelection(
    selectedRowKeys.value,
    (keys) => {
      handleSelectionChange(keys)
      emit('update:selectedRowKeys', keys)
    },
    props.selectionType,
  )
})

// 计算属性：分页配置
const tablePagination = computed(() => {
  return props.pagination
})

/**
 * 处理列配置更新
 */
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  updateColumns(newColumns)
  emit('columnsUpdate', newColumns)
}

/**
 * 处理行高变化
 */
const handleLineHeightChange = (height: string) => {
  updateLineHeight(height as any)
  emit('lineHeightChange', height)
}

/**
 * 获取行操作按钮
 */
const getRowActions = (record: any): ActionButton[] => {
  return props.rowActions.map((action) => ({
    ...action,
    onClick: (rec: any, index: number) => action.onClick(rec || record, index),
  }))
}
</script>

<style scoped>
.configurable-table {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .table-toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}
</style>
