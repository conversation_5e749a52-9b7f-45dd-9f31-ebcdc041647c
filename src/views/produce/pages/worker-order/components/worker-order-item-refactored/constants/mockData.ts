import type { ProductionTask, MaterialItem } from '../types'

/**
 * 生产任务模拟数据
 * 重构改进：
 * 1. 使用了正确的类型定义
 * 2. 提供了更真实的模拟数据
 * 3. 添加了数据生成函数
 */
export const mockTaskData: ProductionTask[] = [
  {
    id: '1',
    taskNo: 'T001',
    processName: '下料',
    processNo: 'P001',
    reportPermission: '班组长',
    assignmentList: '张三,李四',
    workCountRatio: '1:1',
    planCount: 100,
    goodCount: 95,
    badCount: 5,
    badList: '划伤,变形',
    planStartTime: '2024-01-15 08:00',
    planEndTime: '2024-01-15 12:00',
    actualStartTime: '2024-01-15 08:30',
    actualEndTime: '2024-01-15 11:45',
    status: 'completed' as any,
  },
  {
    id: '2',
    taskNo: 'T002',
    processName: '粗加工',
    processNo: 'P002',
    reportPermission: '操作员',
    assignmentList: '王五,赵六',
    workCountRatio: '2:1',
    planCount: 95,
    goodCount: 90,
    badCount: 5,
    badList: '尺寸偏差',
    planStartTime: '2024-01-15 13:00',
    planEndTime: '2024-01-15 17:00',
    actualStartTime: '2024-01-15 13:15',
    actualEndTime: '',
    status: 'in_progress' as any,
  },
  {
    id: '3',
    taskNo: 'T003',
    processName: '精加工',
    processNo: 'P003',
    reportPermission: '技术员',
    assignmentList: '孙七',
    workCountRatio: '1:2',
    planCount: 90,
    goodCount: 0,
    badCount: 0,
    badList: '',
    planStartTime: '2024-01-16 08:00',
    planEndTime: '2024-01-16 16:00',
    actualStartTime: '',
    actualEndTime: '',
    status: 'not_started' as any,
  },
]

/**
 * 物料清单模拟数据
 * 重构改进：
 * 1. 使用了正确的类型定义
 * 2. 提供了更真实的模拟数据
 * 3. 添加了数据生成函数
 */
export const mockMaterialData: MaterialItem[] = [
  {
    id: '1',
    productNo: 'M001',
    productName: '钢板',
    productSpec: '10mm*1000mm*2000mm',
    productAttr: '普通钢',
    stockCount: 500,
    unit: '张',
    unitUsage: 2,
    demandCount: 200,
    unclaimedCount: 150,
    actualUsageCount: 50,
    claimedCount: 50,
    returnedCount: 0,
  },
  {
    id: '2',
    productNo: 'M002',
    productName: '螺栓',
    productSpec: 'M8*25',
    productAttr: '不锈钢',
    stockCount: 10000,
    unit: '个',
    unitUsage: 4,
    demandCount: 400,
    unclaimedCount: 300,
    actualUsageCount: 100,
    claimedCount: 100,
    returnedCount: 5,
  },
  {
    id: '3',
    productNo: 'M003',
    productName: '垫片',
    productSpec: 'Φ8',
    productAttr: '橡胶',
    stockCount: 5000,
    unit: '个',
    unitUsage: 4,
    demandCount: 400,
    unclaimedCount: 400,
    actualUsageCount: 0,
    claimedCount: 0,
    returnedCount: 0,
  },
]

/**
 * 生成模拟任务数据
 * @param count 生成数量
 * @returns 任务数据数组
 */
export const generateMockTaskData = (count: number): ProductionTask[] => {
  const processes = ['下料', '粗加工', '精加工', '热处理', '表面处理', '装配', '检验']
  const permissions = ['班组长', '操作员', '技术员', '质检员']
  const statuses = ['not_started', 'in_progress', 'completed', 'paused'] as const

  return Array.from({ length: count }, (_, index) => ({
    id: String(index + 1),
    taskNo: `T${String(index + 1).padStart(3, '0')}`,
    processName: processes[index % processes.length],
    processNo: `P${String(index + 1).padStart(3, '0')}`,
    reportPermission: permissions[index % permissions.length],
    assignmentList: `操作员${index + 1}`,
    workCountRatio: `${Math.floor(Math.random() * 3) + 1}:1`,
    planCount: Math.floor(Math.random() * 100) + 50,
    goodCount: Math.floor(Math.random() * 50) + 40,
    badCount: Math.floor(Math.random() * 10),
    badList: index % 3 === 0 ? '划伤,变形' : index % 3 === 1 ? '尺寸偏差' : '',
    planStartTime: `2024-01-${String(15 + index).padStart(2, '0')} 08:00`,
    planEndTime: `2024-01-${String(15 + index).padStart(2, '0')} 17:00`,
    actualStartTime: index < 2 ? `2024-01-${String(15 + index).padStart(2, '0')} 08:30` : '',
    actualEndTime: index < 1 ? `2024-01-${String(15 + index).padStart(2, '0')} 16:45` : '',
    status: statuses[index % statuses.length],
  }))
}

/**
 * 生成模拟物料数据
 * @param count 生成数量
 * @returns 物料数据数组
 */
export const generateMockMaterialData = (count: number): MaterialItem[] => {
  const materials = ['钢板', '螺栓', '垫片', '轴承', '密封圈', '弹簧', '齿轮']
  const units = ['张', '个', '套', '米', '公斤']
  const attrs = ['普通钢', '不锈钢', '铝合金', '橡胶', '塑料']

  return Array.from({ length: count }, (_, index) => ({
    id: String(index + 1),
    productNo: `M${String(index + 1).padStart(3, '0')}`,
    productName: materials[index % materials.length],
    productSpec: `规格${index + 1}`,
    productAttr: attrs[index % attrs.length],
    stockCount: Math.floor(Math.random() * 1000) + 100,
    unit: units[index % units.length],
    unitUsage: Math.floor(Math.random() * 5) + 1,
    demandCount: Math.floor(Math.random() * 200) + 50,
    unclaimedCount: Math.floor(Math.random() * 150) + 30,
    actualUsageCount: Math.floor(Math.random() * 50),
    claimedCount: Math.floor(Math.random() * 100) + 20,
    returnedCount: Math.floor(Math.random() * 10),
  }))
}
