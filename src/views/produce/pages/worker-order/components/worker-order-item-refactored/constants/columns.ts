import type { TableColumn } from '@/common/types'

/**
 * 生产任务表格列配置
 * 重构改进：
 * 1. 添加了更详细的类型注释
 * 2. 统一了列宽设置
 * 3. 优化了列的显示顺序
 */
export const taskColumns: TableColumn[] = [
  {
    title: '工序名称',
    dataIndex: 'taskNo',
    key: 'taskNo',
    width: 120,
    visible: true,
  },
  {
    title: '工序编号',
    dataIndex: 'processNo',
    key: 'processNo',
    width: 120,
    visible: true,
  },
  {
    title: '报工权限',
    dataIndex: 'reportPermission',
    key: 'reportPermission',
    width: 120,
    visible: true,
  },
  {
    title: '分配列表',
    dataIndex: 'assignmentList',
    key: 'assignmentList',
    width: 120,
    visible: true,
  },
  {
    title: '报工数配比',
    dataIndex: 'workCountRatio',
    key: 'workCountRatio',
    width: 120,
    visible: true,
  },
  {
    title: '计划数',
    dataIndex: 'planCount',
    key: 'planCount',
    width: 100,
    visible: true,
  },
  {
    title: '良品数',
    dataIndex: 'goodCount',
    key: 'goodCount',
    width: 100,
    visible: true,
  },
  {
    title: '不良品数',
    dataIndex: 'badCount',
    key: 'badCount',
    width: 100,
    visible: true,
  },
  {
    title: '不良品项列表',
    dataIndex: 'badList',
    key: 'badList',
    width: 150,
    visible: true,
  },
  {
    title: '计划开始时间',
    dataIndex: 'planStartTime',
    key: 'planStartTime',
    width: 150,
    visible: true,
  },
  {
    title: '计划结束时间',
    dataIndex: 'planEndTime',
    key: 'planEndTime',
    width: 150,
    visible: true,
  },
  {
    title: '实际开始时间',
    dataIndex: 'actualStartTime',
    key: 'actualStartTime',
    width: 150,
    visible: true,
  },
  {
    title: '实际结束时间',
    dataIndex: 'actualEndTime',
    key: 'actualEndTime',
    width: 150,
    visible: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    visible: true,
  },
]

/**
 * 物料清单表格列配置
 * 重构改进：
 * 1. 添加了更详细的类型注释
 * 2. 统一了列宽设置
 * 3. 优化了列的显示顺序
 */
export const materialColumns: TableColumn[] = [
  {
    title: '产品编号',
    dataIndex: 'productNo',
    key: 'productNo',
    width: 120,
    visible: true,
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 150,
    visible: true,
  },
  {
    title: '产品规格',
    dataIndex: 'productSpec',
    key: 'productSpec',
    width: 120,
    visible: true,
  },
  {
    title: '产品属性',
    dataIndex: 'productAttr',
    key: 'productAttr',
    width: 120,
    visible: true,
  },
  {
    title: '库存数量',
    dataIndex: 'stockCount',
    key: 'stockCount',
    width: 100,
    visible: true,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 80,
    visible: true,
  },
  {
    title: '单位用量',
    dataIndex: 'unitUsage',
    key: 'unitUsage',
    width: 100,
    visible: true,
  },
  {
    title: '需求数量',
    dataIndex: 'demandCount',
    key: 'demandCount',
    width: 100,
    visible: true,
  },
  {
    title: '未领料数量',
    dataIndex: 'unclaimedCount',
    key: 'unclaimedCount',
    width: 120,
    visible: true,
  },
  {
    title: '实际用料数量',
    dataIndex: 'actualUsageCount',
    key: 'actualUsageCount',
    width: 120,
    visible: true,
  },
  {
    title: '已领料数量',
    dataIndex: 'claimedCount',
    key: 'claimedCount',
    width: 120,
    visible: true,
  },
  {
    title: '已退料数量',
    dataIndex: 'returnedCount',
    key: 'returnedCount',
    width: 120,
    visible: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    visible: true,
  },
]

/**
 * 默认的操作按钮配置
 */
export const defaultTaskActions = [
  { label: '编辑', key: 'edit' },
  { label: '复制', key: 'copy' },
  { label: '删除', key: 'delete' },
]

export const defaultMaterialActions = [
  { label: '编辑', key: 'edit' },
  { label: '复制', key: 'copy' },
  { label: '删除', key: 'delete' },
]
