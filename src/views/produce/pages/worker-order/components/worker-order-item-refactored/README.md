# 工单组件重构说明

## 重构概述

本次重构将原本480行的巨型组件拆分为多个职责清晰的小组件，大大提升了代码的可维护性和可扩展性。

## 文件结构

```
worker-order-item-refactored/
├── components/                    # 组件目录
│   ├── WorkerOrderDrawer.vue     # 主容器组件 (抽屉管理)
│   ├── WorkerOrderForm.vue       # 基本信息表单组件
│   ├── ConfigurableTable.vue     # 通用可配置表格组件
│   ├── ProductInfoDisplay.vue    # 产品信息展示组件
│   └── StatisticsCard.vue        # 统计信息卡片组件
├── composables/                   # 组合式函数目录
│   ├── useWorkerOrderForm.ts     # 表单管理
│   ├── useScrollAnchor.ts        # 滚动锚点管理
│   ├── useTableConfig.ts         # 表格配置管理
│   └── useDrawerState.ts         # 抽屉状态管理
├── utils/                         # 工具函数目录
│   ├── timeUtils.ts              # 时间处理工具
│   └── tableUtils.ts             # 表格工具
├── types/                         # 类型定义目录
│   └── index.ts                  # 核心类型定义
├── services/                      # 服务层目录
│   └── workerOrderService.ts     # API服务封装
├── constants/                     # 常量目录
│   ├── columns.ts                # 表格列配置
│   └── mockData.ts               # 模拟数据
├── styles/                        # 样式目录
│   └── index.scss                # 组件样式
├── index.vue                      # 入口组件
└── README.md                      # 说明文档
```

## 重构改进点

### 1. 组件职责分离

- **WorkerOrderDrawer**: 负责抽屉的开关、最大化、标签页导航
- **WorkerOrderForm**: 专门处理基本信息表单逻辑
- **ConfigurableTable**: 通用表格组件，支持列配置、行高调整等
- **ProductInfoDisplay**: 产品信息展示
- **StatisticsCard**: 统计信息展示

### 2. 业务逻辑抽离

- **useWorkerOrderForm**: 表单数据管理和验证
- **useScrollAnchor**: 滚动锚点和标签页联动
- **useTableConfig**: 表格配置管理
- **useDrawerState**: 抽屉状态管理

### 3. 工具函数封装

- **timeUtils**: 时间处理相关工具
- **tableUtils**: 表格操作相关工具

### 4. 类型安全

- 完整的 TypeScript 类型定义
- 接口规范化，便于维护

### 5. 服务层封装

- 统一的 API 调用接口
- 支持模拟数据和真实 API 切换

## 使用方式

```vue
<template>
  <WorkerOrderItemRefactored
    :open="drawerOpen"
    :orderId="currentOrderId"
    :type="operationType"
    @close="handleClose"
  />
</template>

<script setup>
import WorkerOrderItemRefactored from './worker-order-item-refactored/index.vue'

const drawerOpen = ref(false)
const currentOrderId = ref('')
const operationType = ref('create') // 'create' | 'edit' | 'view'

const handleClose = () => {
  drawerOpen.value = false
}
</script>
```

## 组件 Props

| 属性    | 类型                         | 默认值   | 说明                        |
| ------- | ---------------------------- | -------- | --------------------------- |
| open    | boolean                      | false    | 抽屉是否打开                |
| orderId | string \| number             | -        | 工单ID（编辑/查看模式必传） |
| type    | 'create' \| 'edit' \| 'view' | 'create' | 操作类型                    |

## 组件 Events

| 事件名      | 参数    | 说明             |
| ----------- | ------- | ---------------- |
| close       | -       | 抽屉关闭事件     |
| update:open | boolean | 抽屉开关状态更新 |

## 扩展性

### 添加新的表单字段

在 `WorkerOrderForm.vue` 中添加新的表单项，并在 `types/index.ts` 中更新 `WorkerOrder` 接口。

### 添加新的表格列

在 `constants/columns.ts` 中添加新的列配置。

### 添加新的业务逻辑

创建新的 composable 函数，或在现有的 composable 中扩展功能。

### 自定义表格操作

通过 `ConfigurableTable` 组件的 `actionButtons` 和 `rowActions` 属性自定义操作按钮。

## 性能优化

1. **按需加载**: 大组件拆分后可实现更细粒度的懒加载
2. **渲染优化**: 小组件减少不必要的重新渲染
3. **内存优化**: 组件销毁时释放更精确

## 测试建议

1. **单元测试**: 每个 composable 和工具函数都应有对应的单元测试
2. **组件测试**: 使用 Vue Test Utils 测试各个子组件
3. **集成测试**: 测试组件间的交互和数据流

## 迁移指南

1. 保持原有组件不变，新建重构后的组件
2. 逐步替换使用重构后的组件
3. 验证功能一致性后删除原组件

## 注意事项

1. 确保所有依赖的公共组件（如 `ColumnConfig`、`LineHeight`、`CustomTag`）存在
2. 检查 `@/utils` 中的工具函数是否可用
3. 根据实际项目调整 API 服务的实现
4. 样式可能需要根据项目的设计规范进行调整
