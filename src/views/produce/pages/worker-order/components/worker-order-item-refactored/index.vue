<template>
  <WorkerOrderDrawer
    :open="props.open"
    :orderId="props.orderId"
    :type="props.type"
    :submitLoading="submitLoading"
    @close="handleClose"
    @submit="handleSubmit"
    @tab-change="handleTabChange"
  >
    <!-- 基本信息内容 -->
    <template #base-content>
      <WorkerOrderForm
        ref="formRef"
        :type="props.type"
        :orderId="props.orderId"
        @update:form="handleFormUpdate"
        @product-change="handleProductChange"
        @create-product="handleCreateProduct"
        @advanced-select="handleAdvancedSelect"
      />
    </template>

    <!-- 自定义信息内容 -->
    <template #custom-content>
      <div class="custom-info-placeholder">
        <a-empty description="暂无自定义信息配置" />
      </div>
    </template>

    <!-- 生产任务内容 -->
    <template #task-content>
      <ConfigurableTable
        :columns="taskColumns"
        :dataSource="taskData"
        :actionButtons="taskActionButtons"
        :rowActions="taskRowActions"
        :rowSelection="true"
        :loading="taskLoading"
        @columnsUpdate="handleTaskColumnsUpdate"
        @lineHeightChange="handleTaskLineHeightChange"
        @update:selectedRowKeys="handleTaskSelectionChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space size="small">
              <a @click="handleEditTask(record)">编辑</a>
              <a @click="handleCopyTask(record)">复制</a>
              <a @click="handleDeleteTask(record)">删除</a>
            </a-space>
          </template>
        </template>
      </ConfigurableTable>
    </template>

    <!-- 用料清单内容 -->
    <template #bom-content>
      <ConfigurableTable
        :columns="materialColumns"
        :dataSource="materialData"
        :actionButtons="materialActionButtons"
        :rowActions="materialRowActions"
        :rowSelection="true"
        :loading="materialLoading"
        @columnsUpdate="handleMaterialColumnsUpdate"
        @lineHeightChange="handleMaterialLineHeightChange"
        @update:selectedRowKeys="handleMaterialSelectionChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space size="small">
              <a @click="handleEditMaterial(record)">编辑</a>
              <a @click="handleCopyMaterial(record)">复制</a>
              <a @click="handleDeleteMaterial(record)">删除</a>
            </a-space>
          </template>
        </template>
      </ConfigurableTable>
    </template>
  </WorkerOrderDrawer>
</template>

<script setup lang="ts">
import { ref, onMounted, h } from 'vue'
import {
  PlusCircleFilled,
  SaveOutlined,
  FileTextOutlined,
  ReloadOutlined,
} from '@ant-design/icons-vue'
import type { ComponentType, TabKey, ProductionTask, MaterialItem } from './types'
import type { ActionButton } from './types'
import WorkerOrderDrawer from './components/WorkerOrderDrawer.vue'
import WorkerOrderForm from './components/WorkerOrderForm.vue'
import ConfigurableTable from './components/ConfigurableTable.vue'
import { taskColumns, materialColumns } from './constants/columns'
import { WorkerOrderService } from './services/workerOrderService'

/**
 * 重构后的工单组件入口文件
 *
 * 重构改进点：
 * 1. 组件职责清晰分离：抽屉容器、表单、表格各司其职
 * 2. 数据流清晰：通过props和events进行父子组件通信
 * 3. 业务逻辑抽离：使用composables和services管理状态和API调用
 * 4. 可复用性强：通用表格组件可在其他地方复用
 * 5. 类型安全：完整的TypeScript类型定义
 * 6. 易于维护：代码结构清晰，便于后续扩展和修改
 */

interface Props {
  open: boolean
  orderId?: string | number
  type: ComponentType
}

const props = withDefaults(defineProps<Props>(), {
  type: 'create',
})

const emit = defineEmits<{
  'update:open': [open: boolean]
  close: []
}>()

// 组件引用
const formRef = ref()

// 加载状态
const submitLoading = ref(false)
const taskLoading = ref(false)
const materialLoading = ref(false)

// 数据状态
const taskData = ref<ProductionTask[]>([])
const materialData = ref<MaterialItem[]>([])

// 任务表格操作按钮
const taskActionButtons: ActionButton[] = [
  {
    label: '添加任务',
    key: 'add',
    type: 'primary',
    icon: PlusCircleFilled,
    onClick: handleAddTask,
  },
  {
    label: '保存工艺路线',
    key: 'save-route',
    type: 'default',
    icon: SaveOutlined,
    onClick: handleSaveRoute,
  },
]

// 物料表格操作按钮
const materialActionButtons: ActionButton[] = [
  {
    label: '添加用料',
    key: 'add',
    type: 'primary',
    icon: PlusCircleFilled,
    onClick: handleAddMaterial,
  },
  {
    label: '按BOM添加',
    key: 'add-bom',
    type: 'default',
    icon: FileTextOutlined,
    onClick: handleAddByBom,
  },
  {
    label: '保存物料清单',
    key: 'save-list',
    type: 'default',
    icon: SaveOutlined,
    onClick: handleSaveMaterialList,
  },
  {
    label: '更新物料清单',
    key: 'update-list',
    type: 'default',
    icon: ReloadOutlined,
    onClick: handleUpdateMaterialList,
  },
]

// 行操作按钮
const taskRowActions: ActionButton[] = [
  { label: '编辑', key: 'edit', onClick: handleEditTask },
  { label: '复制', key: 'copy', onClick: handleCopyTask },
  { label: '删除', key: 'delete', onClick: handleDeleteTask },
]

const materialRowActions: ActionButton[] = [
  { label: '编辑', key: 'edit', onClick: handleEditMaterial },
  { label: '复制', key: 'copy', onClick: handleCopyMaterial },
  { label: '删除', key: 'delete', onClick: handleDeleteMaterial },
]

/**
 * 生命周期 - 组件挂载
 */
onMounted(async () => {
  if (props.open && props.orderId && props.type !== 'create') {
    await loadOrderData()
  }
  await loadTaskData()
  await loadMaterialData()
})

/**
 * 加载工单数据
 */
const loadOrderData = async () => {
  try {
    if (!props.orderId) return

    const orderData = await WorkerOrderService.getOrderDetail(String(props.orderId))
    formRef.value?.setData(orderData)
  } catch (error) {
    console.error('加载工单数据失败:', error)
  }
}

/**
 * 加载任务数据
 */
const loadTaskData = async () => {
  try {
    taskLoading.value = true
    const orderId = props.orderId || 'new'
    taskData.value = await WorkerOrderService.getTaskList(String(orderId))
  } catch (error) {
    console.error('加载任务数据失败:', error)
  } finally {
    taskLoading.value = false
  }
}

/**
 * 加载物料数据
 */
const loadMaterialData = async () => {
  try {
    materialLoading.value = true
    const orderId = props.orderId || 'new'
    materialData.value = await WorkerOrderService.getMaterialList(String(orderId))
  } catch (error) {
    console.error('加载物料数据失败:', error)
  } finally {
    materialLoading.value = false
  }
}

/**
 * 处理抽屉关闭
 */
const handleClose = () => {
  emit('close')
}

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  try {
    submitLoading.value = true

    // 验证表单
    const isValid = await formRef.value?.validate()
    if (!isValid) return

    // 获取表单数据
    const formData = formRef.value?.getFormData()

    // 提交数据
    if (props.type === 'create') {
      await WorkerOrderService.createOrder(formData)
    } else if (props.type === 'edit') {
      await WorkerOrderService.updateOrder(String(props.orderId), formData)
    }

    // 提交成功，关闭抽屉
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

/**
 * 处理标签页切换
 */
const handleTabChange = (key: TabKey) => {
  console.log('切换到标签页:', key)
}

/**
 * 处理表单更新
 */
const handleFormUpdate = (form: any) => {
  console.log('表单数据更新:', form)
}

/**
 * 处理产品选择变化
 */
const handleProductChange = (productId: string) => {
  console.log('产品选择变化:', productId)
}

/**
 * 处理创建产品
 */
const handleCreateProduct = () => {
  console.log('创建产品')
}

/**
 * 处理高级选择
 */
const handleAdvancedSelect = () => {
  console.log('高级选择')
}

// 任务相关操作
function handleAddTask() {
  console.log('添加任务')
}
function handleSaveRoute() {
  console.log('保存工艺路线')
}
function handleEditTask(record: any) {
  console.log('编辑任务:', record)
}
function handleCopyTask(record: any) {
  console.log('复制任务:', record)
}
function handleDeleteTask(record: any) {
  console.log('删除任务:', record)
}
function handleTaskColumnsUpdate(columns: any) {
  console.log('任务列更新:', columns)
}
function handleTaskLineHeightChange(height: string) {
  console.log('任务行高变化:', height)
}
function handleTaskSelectionChange(keys: any) {
  console.log('任务选择变化:', keys)
}

// 物料相关操作
function handleAddMaterial() {
  console.log('添加物料')
}
function handleAddByBom() {
  console.log('按BOM添加')
}
function handleSaveMaterialList() {
  console.log('保存物料清单')
}
function handleUpdateMaterialList() {
  console.log('更新物料清单')
}
function handleEditMaterial(record: any) {
  console.log('编辑物料:', record)
}
function handleCopyMaterial(record: any) {
  console.log('复制物料:', record)
}
function handleDeleteMaterial(record: any) {
  console.log('删除物料:', record)
}
function handleMaterialColumnsUpdate(columns: any) {
  console.log('物料列更新:', columns)
}
function handleMaterialLineHeightChange(height: string) {
  console.log('物料行高变化:', height)
}
function handleMaterialSelectionChange(keys: any) {
  console.log('物料选择变化:', keys)
}
</script>

<style scoped>
.custom-info-placeholder {
  padding: 40px;
  text-align: center;
}
</style>
