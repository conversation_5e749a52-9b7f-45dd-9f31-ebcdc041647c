import type { WorkerOrder, ProductionTask, MaterialItem, ProductInfo } from '../types'

/**
 * 工单服务类
 * 职责：封装所有与工单相关的API调用
 * 重构改进：
 * 1. 统一的API调用接口
 * 2. 错误处理和类型安全
 * 3. 支持模拟数据和真实API切换
 */
export class WorkerOrderService {
  private static baseUrl = '/api/worker-orders'
  private static useMockData = true // 开发阶段使用模拟数据

  /**
   * 获取工单详情
   * @param orderId 工单ID
   * @returns 工单详情
   */
  static async getOrderDetail(orderId: string): Promise<WorkerOrder> {
    if (this.useMockData) {
      // 返回模拟数据
      return {
        id: orderId,
        orderNo: 'WO20240115001',
        planTime: [new Date('2024-01-15 08:00'), new Date('2024-01-15 18:00')] as any,
        status: 'not_started' as any,
        productId: '1',
        planCount: 100,
        remark: '测试工单',
        relatedOrder: 'SO20240115001',
        goodCount: 0,
        badCount: 0,
        defectRate: 0,
      }
    }

    try {
      const response = await fetch(`${this.baseUrl}/${orderId}`)
      if (!response.ok) {
        throw new Error(`获取工单详情失败: ${response.statusText}`)
      }
      return await response.json()
    } catch (error) {
      console.error('获取工单详情失败:', error)
      throw error
    }
  }

  /**
   * 创建工单
   * @param orderData 工单数据
   * @returns 创建结果
   */
  static async createOrder(orderData: Omit<WorkerOrder, 'id'>): Promise<WorkerOrder> {
    if (this.useMockData) {
      // 模拟创建成功
      return {
        ...orderData,
        id: Date.now().toString(),
      }
    }

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      })

      if (!response.ok) {
        throw new Error(`创建工单失败: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('创建工单失败:', error)
      throw error
    }
  }

  /**
   * 更新工单
   * @param orderId 工单ID
   * @param updates 更新数据
   * @returns 更新结果
   */
  static async updateOrder(orderId: string, updates: Partial<WorkerOrder>): Promise<WorkerOrder> {
    if (this.useMockData) {
      // 模拟更新成功
      const existingOrder = await this.getOrderDetail(orderId)
      return { ...existingOrder, ...updates }
    }

    try {
      const response = await fetch(`${this.baseUrl}/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })

      if (!response.ok) {
        throw new Error(`更新工单失败: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('更新工单失败:', error)
      throw error
    }
  }

  /**
   * 删除工单
   * @param orderId 工单ID
   * @returns 删除结果
   */
  static async deleteOrder(orderId: string): Promise<boolean> {
    if (this.useMockData) {
      // 模拟删除成功
      return true
    }

    try {
      const response = await fetch(`${this.baseUrl}/${orderId}`, {
        method: 'DELETE',
      })

      return response.ok
    } catch (error) {
      console.error('删除工单失败:', error)
      throw error
    }
  }

  /**
   * 获取工单的生产任务列表
   * @param orderId 工单ID
   * @returns 生产任务列表
   */
  static async getTaskList(orderId: string): Promise<ProductionTask[]> {
    if (this.useMockData) {
      const { mockTaskData } = await import('../constants/mockData')
      return mockTaskData
    }

    try {
      const response = await fetch(`${this.baseUrl}/${orderId}/tasks`)
      if (!response.ok) {
        throw new Error(`获取任务列表失败: ${response.statusText}`)
      }
      return await response.json()
    } catch (error) {
      console.error('获取任务列表失败:', error)
      throw error
    }
  }

  /**
   * 获取工单的物料清单
   * @param orderId 工单ID
   * @returns 物料清单
   */
  static async getMaterialList(orderId: string): Promise<MaterialItem[]> {
    if (this.useMockData) {
      const { mockMaterialData } = await import('../constants/mockData')
      return mockMaterialData
    }

    try {
      const response = await fetch(`${this.baseUrl}/${orderId}/materials`)
      if (!response.ok) {
        throw new Error(`获取物料清单失败: ${response.statusText}`)
      }
      return await response.json()
    } catch (error) {
      console.error('获取物料清单失败:', error)
      throw error
    }
  }

  /**
   * 获取产品列表
   * @param keyword 搜索关键词
   * @returns 产品列表
   */
  static async getProductList(keyword?: string): Promise<ProductInfo[]> {
    if (this.useMockData) {
      // 返回模拟产品数据
      const mockProducts: ProductInfo[] = [
        {
          id: '1',
          productNo: 'CP20230004',
          productName: 'G031',
          productSpec: '1250T',
          stockCount: 1000,
          unit: '只',
        },
        {
          id: '2',
          productNo: 'CP20230005',
          productName: 'G032',
          productSpec: '1500T',
          stockCount: 800,
          unit: '只',
        },
      ]

      if (keyword) {
        return mockProducts.filter(
          (p) => p.productName.includes(keyword) || p.productNo.includes(keyword),
        )
      }

      return mockProducts
    }

    try {
      const url = keyword ? `/api/products?keyword=${encodeURIComponent(keyword)}` : '/api/products'

      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`获取产品列表失败: ${response.statusText}`)
      }
      return await response.json()
    } catch (error) {
      console.error('获取产品列表失败:', error)
      throw error
    }
  }

  /**
   * 切换数据源模式
   * @param useMock 是否使用模拟数据
   */
  static setMockMode(useMock: boolean) {
    this.useMockData = useMock
  }
}
