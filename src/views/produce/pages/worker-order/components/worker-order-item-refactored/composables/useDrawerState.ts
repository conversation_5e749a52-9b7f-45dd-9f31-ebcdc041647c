import { ref, computed } from 'vue'
import type { DrawerConfig } from '../types'

/**
 * 抽屉状态管理 Composable
 * 职责：管理抽屉的开关、最大化、宽度等状态
 */
export function useDrawerState() {
  // 抽屉是否打开
  const open = ref(false)

  // 是否最大化
  const isMaximize = ref(false)

  // 默认宽度
  const defaultWidth = '1536px'

  // 计算属性：抽屉宽度
  const drawerWidth = computed(() => {
    return isMaximize.value ? '100%' : defaultWidth
  })

  // 计算属性：抽屉配置对象
  const drawerConfig = computed<DrawerConfig>(() => ({
    open: open.value,
    width: drawerWidth.value,
    isMaximize: isMaximize.value,
  }))

  /**
   * 打开抽屉
   */
  const openDrawer = () => {
    open.value = true
  }

  /**
   * 关闭抽屉
   */
  const closeDrawer = () => {
    open.value = false
    // 关闭时重置最大化状态
    isMaximize.value = false
  }

  /**
   * 切换抽屉状态
   */
  const toggleDrawer = () => {
    if (open.value) {
      closeDrawer()
    } else {
      openDrawer()
    }
  }

  /**
   * 最大化/还原抽屉
   */
  const handleMaximize = () => {
    isMaximize.value = !isMaximize.value
  }

  /**
   * 设置抽屉宽度（非最大化状态下）
   * @param width 宽度值
   */
  const setDrawerWidth = (width: string) => {
    if (!isMaximize.value) {
      // 这里可以扩展为支持自定义宽度
      console.log('设置抽屉宽度:', width)
    }
  }

  /**
   * 重置抽屉状态
   */
  const resetDrawerState = () => {
    open.value = false
    isMaximize.value = false
  }

  /**
   * 获取抽屉样式
   */
  const getDrawerStyle = () => {
    return {
      width: drawerWidth.value,
      maxWidth: '100%',
    }
  }

  /**
   * 获取抽屉类名
   */
  const getDrawerClass = () => {
    return {
      'drawer-maximized': isMaximize.value,
      'drawer-normal': !isMaximize.value,
    }
  }

  return {
    // 响应式数据
    open,
    isMaximize,

    // 计算属性
    drawerWidth,
    drawerConfig,

    // 方法
    openDrawer,
    closeDrawer,
    toggleDrawer,
    handleMaximize,
    setDrawerWidth,
    resetDrawerState,
    getDrawerStyle,
    getDrawerClass,
  }
}
