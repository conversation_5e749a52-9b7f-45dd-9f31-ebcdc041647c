import { ref, computed, type Ref } from 'vue'
import type { WorkerOrder, ComponentType, FormRules } from '../types'
import { createDefaultTimeRange, formatPlanTime, checkTimeExpired } from '../utils/timeUtils'

/**
 * 工单表单管理 Composable
 * 职责：管理工单表单数据、验证规则、提交逻辑
 */
export function useWorkerOrderForm(type: ComponentType) {
  // 表单数据
  const form = ref<WorkerOrder>({
    orderNo: '',
    planTime: createDefaultTimeRange(),
    status: 'not_started' as any,
    productId: '',
    planCount: 0,
    remark: '',
    relatedOrder: '',
  })

  // 表单验证规则
  const rules = computed<FormRules>(() => ({
    planTime: [{ required: true, message: '请选择计划时间', trigger: 'change' }],
    productId: [{ required: true, message: '请选择产品信息', trigger: 'change' }],
    planCount: [
      { required: true, message: '请输入计划数', trigger: 'blur' },
      { type: 'number', min: 1, message: '计划数必须大于0', trigger: 'blur' },
    ],
  }))

  // 计算属性：是否过期
  const isExpired = computed(() => {
    return checkTimeExpired(form.value.planTime[1])
  })

  // 计算属性：表单是否只读
  const isReadonly = computed(() => type === 'view')

  // 计算属性：是否为创建模式
  const isCreateMode = computed(() => type === 'create')

  // 计算属性：是否为编辑模式
  const isEditMode = computed(() => type === 'edit')

  /**
   * 验证表单
   */
  const validateForm = async (): Promise<boolean> => {
    try {
      // 这里应该调用 ant-design-vue 的表单验证方法
      // 由于没有表单实例，这里只做基本验证
      const { planTime, productId, planCount } = form.value

      if (!planTime || !planTime[0] || !planTime[1]) {
        throw new Error('请选择计划时间')
      }

      if (!productId) {
        throw new Error('请选择产品信息')
      }

      if (!planCount || planCount <= 0) {
        throw new Error('请输入有效的计划数')
      }

      return true
    } catch (error) {
      console.error('表单验证失败:', error)
      return false
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    form.value = {
      orderNo: '',
      planTime: createDefaultTimeRange(),
      status: 'not_started' as any,
      productId: '',
      planCount: 0,
      remark: '',
      relatedOrder: '',
    }
  }

  /**
   * 设置表单数据
   */
  const setFormData = (data: Partial<WorkerOrder>) => {
    Object.assign(form.value, data)
  }

  /**
   * 获取提交数据
   */
  const getSubmitData = () => {
    const { planTime, ...rest } = form.value
    return {
      ...rest,
      planTime: formatPlanTime(planTime),
    }
  }

  /**
   * 提交表单
   */
  const submitForm = async (): Promise<any> => {
    const isValid = await validateForm()
    if (!isValid) {
      throw new Error('表单验证失败')
    }

    return getSubmitData()
  }

  return {
    // 响应式数据
    form,
    rules,

    // 计算属性
    isExpired,
    isReadonly,
    isCreateMode,
    isEditMode,

    // 方法
    validateForm,
    resetForm,
    setFormData,
    getSubmitData,
    submitForm,
  }
}
