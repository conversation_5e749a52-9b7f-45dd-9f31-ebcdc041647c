import { ref, nextTick, onMounted, onUnmounted, type Ref } from 'vue'
import type { TabKey, AnchorMap } from '../types'

/**
 * 滚动锚点管理 Composable
 * 职责：处理标签页切换时的滚动定位和滚动时的标签页自动切换
 */
export function useScrollAnchor(containerRef: Ref<HTMLElement | undefined>) {
  // 当前激活的标签页
  const activeTab = ref<TabKey>('base')

  // 是否正在滚动到锚点（防止滚动时触发标签页切换）
  const isScrollingToAnchor = ref(false)

  // 锚点映射配置
  const anchorMap: AnchorMap = {
    base: 'base-section',
    custom: 'custom-section',
    task: 'task-section',
    bom: 'bom-section',
  }

  /**
   * 点击标签页时滚动到对应位置
   * @param key 标签页key
   */
  const handleTabChange = (key: TabKey) => {
    const targetId = anchorMap[key]
    const targetElement = document.getElementById(targetId)

    if (targetElement && containerRef.value) {
      isScrollingToAnchor.value = true

      // 计算目标位置，使用offsetTop获取相对位置
      const scrollTop = targetElement.offsetTop - 24 // 24px为padding

      // 平滑滚动
      containerRef.value.scrollTo({
        top: scrollTop,
        behavior: 'smooth',
      })

      // 滚动完成后重置标识
      setTimeout(() => {
        isScrollingToAnchor.value = false
      }, 500)
    }
  }

  /**
   * 监听滚动，更新当前激活的标签页
   */
  const handleScroll = () => {
    if (isScrollingToAnchor.value || !containerRef.value) return

    const container = containerRef.value
    const scrollTop = container.scrollTop

    // 获取各个section的位置信息
    const sections = Object.entries(anchorMap)
      .map(([key, id]) => {
        const element = document.getElementById(id)
        if (element) {
          // 计算元素相对于滚动容器的偏移量
          const offsetTop = element.offsetTop
          return { key: key as TabKey, offsetTop, element }
        }
        return null
      })
      .filter(Boolean) as Array<{ key: TabKey; offsetTop: number; element: HTMLElement }>

    // 按照offsetTop排序
    sections.sort((a, b) => a.offsetTop - b.offsetTop)

    // 找到当前应该激活的section
    let currentSection = sections[0]
    for (const section of sections) {
      // 如果滚动位置超过了section的位置（加上一个偏移量）
      if (scrollTop + 150 >= section.offsetTop) {
        currentSection = section
      } else {
        break
      }
    }

    // 更新激活的标签页
    if (currentSection && activeTab.value !== currentSection.key) {
      activeTab.value = currentSection.key
    }
  }

  /**
   * 滚动到指定锚点
   * @param anchorId 锚点ID
   */
  const scrollToAnchor = (anchorId: string) => {
    const targetElement = document.getElementById(anchorId)
    if (targetElement && containerRef.value) {
      isScrollingToAnchor.value = true

      const scrollTop = targetElement.offsetTop - 24
      containerRef.value.scrollTo({
        top: scrollTop,
        behavior: 'smooth',
      })

      setTimeout(() => {
        isScrollingToAnchor.value = false
      }, 500)
    }
  }

  /**
   * 获取当前可见的section
   */
  const getCurrentVisibleSection = (): TabKey | null => {
    if (!containerRef.value) return null

    const container = containerRef.value
    const scrollTop = container.scrollTop
    const containerHeight = container.clientHeight

    for (const [key, id] of Object.entries(anchorMap)) {
      const element = document.getElementById(id)
      if (element) {
        const elementTop = element.offsetTop
        const elementBottom = elementTop + element.offsetHeight

        // 检查元素是否在可视区域内
        if (elementTop <= scrollTop + containerHeight && elementBottom >= scrollTop) {
          return key as TabKey
        }
      }
    }

    return null
  }

  // 生命周期管理
  onMounted(() => {
    nextTick(() => {
      if (containerRef.value) {
        containerRef.value.addEventListener('scroll', handleScroll)
      }
    })
  })

  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
  })

  return {
    // 响应式数据
    activeTab,
    isScrollingToAnchor,
    anchorMap,

    // 方法
    handleTabChange,
    handleScroll,
    scrollToAnchor,
    getCurrentVisibleSection,
  }
}
