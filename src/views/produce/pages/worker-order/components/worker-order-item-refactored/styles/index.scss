/**
 * 重构后的工单组件样式文件
 * 保持与原组件一致的样式，同时优化了结构
 */

// 抽屉头部样式
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .drawer-header-title {
    font-size: 16px;
    font-weight: 600;
    color: #111;
  }

  .drawer-header-btn {
    display: flex;
    align-items: center;
  }
}

// 抽屉内容样式
.drawer-content {
  padding: 24px;
  height: calc(100vh - 100px); // 减去header和footer的高度
  overflow-y: auto;
  scroll-behavior: smooth;

  .drawer-content-item {
    margin-bottom: 16px;

    &__title {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

// 抽屉底部样式
.drawer-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  .drawer-footer__btn {
    min-width: 80px;
  }
}

// 详情框通用样式
.detail-box {
  display: flex;
  margin-top: 4px;

  .info-block {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 8px 12px;
  }
}

// 产品详情样式
.product-detail {
  gap: 12px;

  &__left {
    flex: 1;
  }

  &__right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 120px;
  }
}

.product-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    color: #666;
    font-size: 12px;
  }

  &__value {
    color: #333;
    font-size: 12px;
    font-weight: 500;
  }
}

.stock-amount {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  line-height: 1;
}

.stock-unit-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  text-align: center;

  span {
    color: #333;
  }
}

// 计划数统计样式
.plan-count-extra {
  gap: 8px;

  .plan-count__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;

    .amount {
      font-size: 16px;
      font-weight: 600;
      line-height: 1;
      margin-bottom: 4px;

      &.good {
        color: #52c41a;
      }

      &.bad {
        color: #ff4d4f;
      }

      &.rate {
        color: #fa8c16;
      }
    }

    .label {
      font-size: 12px;
      color: #666;
    }
  }
}

// 计划时间样式
.plan-time {
  font-size: 12px;
  color: #666;

  &.plan-time-expired {
    color: #ff4d4f;
  }
}

// 状态框样式
.status-box {
  display: flex;
  align-items: center;
}

// 产品信息操作样式
.product-info {
  display: flex;
  align-items: center;
  font-size: 12px;

  .product-info-btn {
    color: #1890ff;
    cursor: pointer;

    &:hover {
      color: #40a9ff;
    }
  }
}

// 表格工具栏样式
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .table-toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

// 自定义信息占位符样式
.custom-info-placeholder {
  padding: 40px;
  text-align: center;
}

// 响应式设计
@media (max-width: 1200px) {
  .drawer-content {
    padding: 16px;
  }

  .product-detail {
    flex-direction: column;
    gap: 8px;

    &__right {
      min-width: auto;
      align-items: flex-start;
    }
  }

  .plan-count-extra {
    flex-wrap: wrap;
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .info-block {
    background-color: #1f1f1f;
    color: #fff;
  }

  .product-row__label {
    color: #999;
  }

  .product-row__value {
    color: #fff;
  }

  .stock-unit-text {
    color: #999;

    span {
      color: #fff;
    }
  }
}
