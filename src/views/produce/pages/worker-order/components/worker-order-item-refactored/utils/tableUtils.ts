import type { TableColumn } from '@/common/types'
import type { ActionButton } from '../types'

/**
 * 创建表格行样式
 * @param lineHeight 行高设置
 * @returns 行样式对象
 */
export const createRowStyle = (lineHeight: string) => {
  const heightMap = {
    low: '32px',
    medium: '64px',
    high: '120px',
  }

  return {
    style: {
      height: heightMap[lineHeight as keyof typeof heightMap] || '64px',
    },
  }
}

/**
 * 过滤可见的表格列
 * @param columns 所有列配置
 * @returns 可见的列配置
 */
export const filterVisibleColumns = (columns: TableColumn[]): TableColumn[] => {
  return columns.filter((column) => column.visible !== false)
}

/**
 * 生成操作按钮
 * @param actions 操作配置数组
 * @returns 操作按钮组件数组
 */
export const generateActionButtons = (actions: ActionButton[]) => {
  return actions.map((action) => ({
    ...action,
    key: action.key || action.label,
  }))
}

/**
 * 更新列的可见性
 * @param columns 当前列配置
 * @param visibleKeys 可见列的key数组
 * @returns 更新后的列配置
 */
export const updateColumnsVisibility = (
  columns: TableColumn[],
  visibleKeys: string[],
): TableColumn[] => {
  return columns.map((column) => ({
    ...column,
    visible: visibleKeys.includes(column.key as string),
  }))
}

/**
 * 获取表格滚动配置
 * @param hasSelection 是否有选择列
 * @param fixedWidth 固定宽度
 * @param maxHeight 最大高度
 * @returns 滚动配置对象
 */
export const getTableScrollConfig = (hasSelection = false, fixedWidth = 3200, maxHeight = 600) => {
  return {
    x: hasSelection ? fixedWidth + 60 : fixedWidth, // 选择列额外增加60px
    y: maxHeight,
  }
}

/**
 * 创建表格选择配置
 * @param selectedRowKeys 已选择的行key
 * @param onChange 选择变化回调
 * @param type 选择类型
 * @returns 选择配置对象
 */
export const createRowSelection = (
  selectedRowKeys: (string | number)[],
  onChange: (keys: (string | number)[]) => void,
  type: 'checkbox' | 'radio' = 'checkbox',
) => {
  return {
    type,
    selectedRowKeys,
    onChange,
  }
}

/**
 * 格式化表格数据
 * @param data 原始数据
 * @param keyField 主键字段名
 * @returns 格式化后的数据
 */
export const formatTableData = (data: any[], keyField = 'id') => {
  return data.map((item, index) => ({
    ...item,
    key: item[keyField] || index,
  }))
}

/**
 * 计算表格列宽
 * @param columns 列配置
 * @param containerWidth 容器宽度
 * @returns 更新宽度后的列配置
 */
export const calculateColumnWidths = (
  columns: TableColumn[],
  containerWidth: number,
): TableColumn[] => {
  const fixedColumns = columns.filter((col) => col.width)
  const flexColumns = columns.filter((col) => !col.width)

  const fixedWidth = fixedColumns.reduce((sum, col) => sum + (col.width as number), 0)
  const remainingWidth = containerWidth - fixedWidth
  const flexWidth = Math.floor(remainingWidth / flexColumns.length)

  return columns.map((col) => ({
    ...col,
    width: col.width || flexWidth,
  }))
}
