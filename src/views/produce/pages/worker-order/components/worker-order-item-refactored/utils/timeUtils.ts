import dayjs, { type Dayjs } from 'dayjs'

/**
 * 格式化计划时间范围
 * @param timeRange 时间范围数组
 * @returns 格式化后的时间字符串数组
 */
export const formatPlanTime = (timeRange: [Dayjs, Dayjs]): [string, string] => {
  return timeRange.map((time) => time.format('YYYY-MM-DD HH:mm')) as [string, string]
}

/**
 * 检查时间是否已过期
 * @param endTime 结束时间
 * @returns 是否过期
 */
export const checkTimeExpired = (endTime: Dayjs): boolean => {
  return endTime.isBefore(dayjs())
}

/**
 * 获取剩余时间文本
 * @param endTime 结束时间
 * @returns 剩余时间描述文本
 */
export const getRemainingTimeText = (endTime: Dayjs): string => {
  const now = dayjs()
  const diff = endTime.diff(now)

  if (diff <= 0) {
    return '已过期'
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) {
    return `剩余 ${days} 天 ${hours} 小时`
  } else if (hours > 0) {
    return `剩余 ${hours} 小时 ${minutes} 分钟`
  } else {
    return `剩余 ${minutes} 分钟`
  }
}

/**
 * 创建默认的时间范围（当天开始到结束）
 * @returns 默认时间范围
 */
export const createDefaultTimeRange = (): [Dayjs, Dayjs] => {
  return [dayjs().startOf('day'), dayjs().endOf('day')]
}

/**
 * 验证时间范围是否有效
 * @param timeRange 时间范围
 * @returns 是否有效
 */
export const validateTimeRange = (timeRange: [Dayjs, Dayjs]): boolean => {
  const [startTime, endTime] = timeRange
  return startTime.isBefore(endTime)
}

/**
 * 格式化时间显示
 * @param time 时间对象
 * @param format 格式化字符串
 * @returns 格式化后的时间字符串
 */
export const formatTime = (time: Dayjs | string, format = 'YYYY-MM-DD HH:mm'): string => {
  if (typeof time === 'string') {
    return dayjs(time).format(format)
  }
  return time.format(format)
}
